import { Injectable, BadRequestException, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { BlockGame } from '../../../database/entities/block-game.entity';
import { BlockGameSentence } from '../../../database/entities/block-game-sentence.entity';
import { BlockGameAttempt } from '../../../database/entities/block-game-attempt.entity';
import { User, UserType } from '../../../database/entities/user.entity';
import { BlockGameDetailDto, BlockGameWordsDto, SubmitBlockGameDto, BlockGameAttemptResultDto } from '../../../database/models/block-game/block-game-student.dto';

@Injectable()
export class BlockGameService {
  private readonly logger = new Logger(BlockGameService.name);

  constructor(
    @InjectRepository(BlockGame)
    private readonly blockGameRepository: Repository<BlockGame>,
    @InjectRepository(BlockGameSentence)
    private readonly blockGameSentenceRepository: Repository<BlockGameSentence>,
    @InjectRepository(BlockGameAttempt)
    private readonly blockGameAttemptRepository: Repository<BlockGameAttempt>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Get a random active block game for student to play
   */
  async getRandomBlockGame(studentId: string): Promise<BlockGameDetailDto> {
    try {
      // Verify student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId, type: UserType.STUDENT },
      });

      if (!student) {
        throw new NotFoundException('Student not found');
      }

      // Get a random active block game with sentences
      const blockGame = await this.blockGameRepository
        .createQueryBuilder('blockGame')
        .leftJoinAndSelect('blockGame.sentences', 'sentences')
        .where('blockGame.isActive = :isActive', { isActive: true })
        .andWhere('sentences.id IS NOT NULL') // Ensure game has sentences
        .orderBy('RANDOM()') // PostgreSQL random ordering
        .getOne();

      if (!blockGame) {
        throw new NotFoundException('No games available at the moment. Please try again later.');
      }

      if (!blockGame.sentences || blockGame.sentences.length === 0) {
        throw new BadRequestException('No games with sentences available. Please try again later.');
      }

      // Sort sentences by order
      const sortedSentences = blockGame.sentences.sort((a, b) => a.sentenceOrder - b.sentenceOrder);

      // Generate randomized word blocks
      const wordBlocks = this.generateWordBlocks(sortedSentences);

      // Map sentences to response format
      const sentenceResponses = sortedSentences.map((sentence) => ({
        starting_part: sentence.startingPart,
        expanding_part: sentence.expandingPart,
        sentence_order: sentence.sentenceOrder,
      }));

      return {
        id: blockGame.id,
        title: blockGame.title,
        score: blockGame.score,
        sentence_count: sortedSentences.length,
        sentences: sentenceResponses,
        word_blocks: wordBlocks,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Failed to get random block game for student ${studentId}: ${error.message}`, error.stack);
      throw new BadRequestException('Could not retrieve a game at this time. Please try again later.');
    }
  }

  /**
   * Get a block game for playing with randomized word blocks
   */
  async getBlockGameForPlay(gameId: string, studentId: string): Promise<BlockGameDetailDto> {
    try {
      // Verify student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId, type: UserType.STUDENT },
      });

      if (!student) {
        throw new NotFoundException('Student not found');
      }

      // Get the block game with sentences
      const blockGame = await this.blockGameRepository.findOne({
        where: { id: gameId, isActive: true },
        relations: ['sentences'],
      });

      if (!blockGame) {
        throw new NotFoundException('Game not found or not available');
      }

      if (!blockGame.sentences || blockGame.sentences.length === 0) {
        throw new BadRequestException('This game has no sentences available. Please try another game.');
      }

      // Sort sentences by order
      const sortedSentences = blockGame.sentences.sort((a, b) => a.sentenceOrder - b.sentenceOrder);

      // Generate randomized word blocks
      const wordBlocks = this.generateWordBlocks(sortedSentences);

      // Map sentences to response format
      const sentenceResponses = sortedSentences.map((sentence) => ({
        starting_part: sentence.startingPart,
        expanding_part: sentence.expandingPart,
        sentence_order: sentence.sentenceOrder,
      }));

      return {
        id: blockGame.id,
        title: blockGame.title,
        score: blockGame.score,
        sentence_count: sortedSentences.length,
        sentences: sentenceResponses,
        word_blocks: wordBlocks,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Failed to get block game ${gameId} for student ${studentId}: ${error.message}`, error.stack);
      throw new BadRequestException('Could not retrieve game at this time. Please try again later.');
    }
  }

  /**
   * Submit a block game attempt and calculate score
   */
  async submitAttempt(studentId: string, dto: SubmitBlockGameDto): Promise<BlockGameAttemptResultDto> {
    try {
      // Verify student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId, type: UserType.STUDENT },
      });

      if (!student) {
        throw new NotFoundException('Student not found');
      }

      // Validate input
      if (!dto.sentence_constructions || dto.sentence_constructions.length === 0) {
        throw new BadRequestException('At least one sentence construction must be provided');
      }

      // Validate each sentence construction
      for (let i = 0; i < dto.sentence_constructions.length; i++) {
        const construction = dto.sentence_constructions[i];
        if (!construction.starting_sentence || !construction.starting_sentence.trim()) {
          throw new BadRequestException(`Starting sentence is required for construction ${i + 1}`);
        }
        if (!construction.expanding_sentence || !construction.expanding_sentence.trim()) {
          throw new BadRequestException(`Expanding sentence is required for construction ${i + 1}`);
        }
      }

      // Get the block game with sentences
      const blockGame = await this.blockGameRepository.findOne({
        where: { id: dto.block_game_id, isActive: true },
        relations: ['sentences'],
      });

      if (!blockGame) {
        throw new NotFoundException('Game not found or not available');
      }

      if (!blockGame.sentences || blockGame.sentences.length === 0) {
        throw new BadRequestException('This game has no sentences available.');
      }

      // Validate that the number of constructions matches the number of sentences
      if (dto.sentence_constructions.length !== blockGame.sentences.length) {
        throw new BadRequestException(
          `Expected ${blockGame.sentences.length} sentence constructions, but received ${dto.sentence_constructions.length}`
        );
      }

      const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Sort sentences by order for consistent comparison
      const correctSentences = blockGame.sentences.sort((a, b) => a.sentenceOrder - b.sentenceOrder);

      // Calculate score and detailed results
      const { score, sentenceResults } = this.calculateScore(dto.sentence_constructions, correctSentences, blockGame.score);

      // Store sentence constructions for the attempt
      const sentenceConstructions = {
        startingSentences: dto.sentence_constructions.map((sc) => sc.starting_sentence),
        expandingSentences: dto.sentence_constructions.map((sc) => sc.expanding_sentence),
        completedSentences: sentenceResults.map((result) => ({
          starting: result.student_starting,
          expanding: result.student_expanding,
          isCorrect: result.is_correct,
          sentenceOrder: result.sentence_order,
        })),
      };

      // Create attempt record
      const attempt = queryRunner.manager.create(BlockGameAttempt, {
        studentId,
        blockGameId: dto.block_game_id,
        score,
        totalScore: blockGame.score,
        sentenceConstructions,
        submittedAt: new Date(),
      });

      await queryRunner.manager.save(attempt);

      await queryRunner.commitTransaction();

      // Return detailed results
      const percentage = Math.round((score / blockGame.score) * 100);

      return {
        score,
        total_score: blockGame.score,
        percentage,
        sentence_results: sentenceResults,
        submitted_at: attempt.submittedAt,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to submit block game attempt for student ${studentId}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException('Could not process your submission. Please try again.');
    } finally {
      await queryRunner.release();
    }
    } catch (error) {
      this.logger.error(`Failed to submit block game attempt for student ${studentId}: ${error.message}`, error.stack);

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new BadRequestException('Could not process your submission. Please try again.');
    }
  }

  /**
   * Generate randomized word blocks from sentences
   */
  private generateWordBlocks(sentences: BlockGameSentence[]): BlockGameWordsDto {
    const startingWords: string[] = [];
    const expandingWords: string[] = [];

    // Extract all words from starting and expanding parts
    sentences.forEach((sentence) => {
      const startWords = sentence.startingPart.trim().split(/\s+/);
      const expandWords = sentence.expandingPart.trim().split(/\s+/);

      startingWords.push(...startWords);
      expandingWords.push(...expandWords);
    });

    // Randomize the word arrays
    return {
      starting_words: this.shuffleArray([...startingWords]),
      expanding_words: this.shuffleArray([...expandingWords]),
    };
  }

  /**
   * Calculate score based on sentence constructions
   */
  private calculateScore(studentConstructions: any[], correctSentences: BlockGameSentence[], totalScore: number) {
    const pointsPerSentence = totalScore / correctSentences.length;
    let score = 0;
    const sentenceResults: any[] = [];

    // Create a map of correct sentences by order
    const correctSentenceMap = correctSentences.reduce((map, sentence) => {
      map[sentence.sentenceOrder] = sentence;
      return map;
    }, {});

    // Evaluate each student construction
    studentConstructions.forEach((construction, index) => {
      // Use sentence_order if provided, otherwise use array index + 1
      const sentenceOrder = construction.sentence_order || (index + 1);
      const correctSentence = correctSentenceMap[sentenceOrder];

      if (correctSentence) {
        // Check if both starting and expanding parts match exactly (case-insensitive, trimmed)
        const studentStarting = construction.starting_sentence.trim().toLowerCase();
        const studentExpanding = construction.expanding_sentence.trim().toLowerCase();
        const correctStarting = correctSentence.startingPart.trim().toLowerCase();
        const correctExpanding = correctSentence.expandingPart.trim().toLowerCase();

        const isCorrect = studentStarting === correctStarting && studentExpanding === correctExpanding;

        if (isCorrect) {
          score += pointsPerSentence;
        }

        sentenceResults.push({
          sentence_order: sentenceOrder,
          student_starting: construction.starting_sentence.trim(),
          student_expanding: construction.expanding_sentence.trim(),
          correct_starting: correctSentence.startingPart,
          correct_expanding: correctSentence.expandingPart,
          is_correct: isCorrect,
          points_earned: isCorrect ? pointsPerSentence : 0,
        });
      }
    });

    return {
      score: Math.round(score),
      sentenceResults: sentenceResults.sort((a, b) => a.sentence_order - b.sentence_order),
    };
  }

  /**
   * Shuffle array using Fisher-Yates algorithm
   */
  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}
