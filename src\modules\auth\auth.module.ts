import { forwardRef, Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { AuthService } from './auth.service';
import { JwtStrategy } from './jwt.strategy';
import { UsersModule } from '../users/users.module';
import { DiaryModule } from '../diary/diary.module';
import { AuthController } from './auth.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Role } from '../../database/entities/role.entity';
import { User } from '../../database/entities/user.entity';
import { UserOtp } from '../../database/entities/user-otp.entity';
import { PasswordReset } from '../../database/entities/password-reset.entity';
import { EmailVerification } from '../../database/entities/email-verification.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { TutorApproval } from '../../database/entities/tutor-approval.entity';
import { Diary } from '../../database/entities/diary.entity';
import { NotificationModule } from '../notification/notification.module';
import { DeeplinkModule } from '../../common/utils/deeplink.module';
import { CommonModule } from '../../common/common.module';
import { PlansModule } from '../plans/plans.module';

@Module({
  imports: [
    forwardRef(() => UsersModule),
    forwardRef(() => DiaryModule),
    forwardRef(() => PlansModule),
    NotificationModule,
    DeeplinkModule,
    CommonModule,
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const secret = configService.get<string>('JWT_SECRET');
        if (!secret) {
          console.error('JWT_SECRET is not defined in environment variables');
          throw new Error('JWT_SECRET is not defined');
        }
        return {
          secret,
          signOptions: { expiresIn: '60m' },
        };
      },
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([Role, User, UserOtp, PasswordReset, EmailVerification, UserPlan, TutorApproval, Diary]),
  ],
  providers: [AuthService, JwtStrategy],
  controllers: [AuthController],
  exports: [AuthService, JwtModule],
})
export class AuthModule {}
