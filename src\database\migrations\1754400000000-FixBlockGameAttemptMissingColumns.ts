import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixBlockGameAttemptMissingColumns1754400000000 implements MigrationInterface {
  name = 'FixBlockGameAttemptMissingColumns1754400000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if block_game_attempt table exists
    const tableExists = await queryRunner.hasTable('block_game_attempt');
    
    if (!tableExists) {
      console.log('block_game_attempt table does not exist, skipping migration');
      return;
    }

    // Check and add score column if it doesn't exist
    const hasScoreColumn = await queryRunner.hasColumn('block_game_attempt', 'score');
    if (!hasScoreColumn) {
      console.log('Adding score column to block_game_attempt table');
      await queryRunner.query(`
        ALTER TABLE "block_game_attempt" 
        ADD COLUMN "score" real NOT NULL DEFAULT 0
      `);
    } else {
      console.log('score column already exists in block_game_attempt table');
    }

    // Check and add total_score column if it doesn't exist
    const hasTotalScoreColumn = await queryRunner.hasColumn('block_game_attempt', 'total_score');
    if (!hasTotalScoreColumn) {
      console.log('Adding total_score column to block_game_attempt table');
      await queryRunner.query(`
        ALTER TABLE "block_game_attempt" 
        ADD COLUMN "total_score" real NOT NULL DEFAULT 0
      `);
    } else {
      console.log('total_score column already exists in block_game_attempt table');
    }

    // Check and add sentence_constructions column if it doesn't exist
    const hasSentenceConstructionsColumn = await queryRunner.hasColumn('block_game_attempt', 'sentence_constructions');
    if (!hasSentenceConstructionsColumn) {
      console.log('Adding sentence_constructions column to block_game_attempt table');
      await queryRunner.query(`
        ALTER TABLE "block_game_attempt" 
        ADD COLUMN "sentence_constructions" jsonb NOT NULL DEFAULT '{}'::jsonb
      `);
    } else {
      console.log('sentence_constructions column already exists in block_game_attempt table');
    }

    console.log('FixBlockGameAttemptMissingColumns migration completed successfully');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if block_game_attempt table exists
    const tableExists = await queryRunner.hasTable('block_game_attempt');
    
    if (!tableExists) {
      console.log('block_game_attempt table does not exist, skipping rollback');
      return;
    }

    // Remove sentence_constructions column if it exists
    const hasSentenceConstructionsColumn = await queryRunner.hasColumn('block_game_attempt', 'sentence_constructions');
    if (hasSentenceConstructionsColumn) {
      console.log('Removing sentence_constructions column from block_game_attempt table');
      await queryRunner.query(`
        ALTER TABLE "block_game_attempt" 
        DROP COLUMN "sentence_constructions"
      `);
    }

    // Remove total_score column if it exists
    const hasTotalScoreColumn = await queryRunner.hasColumn('block_game_attempt', 'total_score');
    if (hasTotalScoreColumn) {
      console.log('Removing total_score column from block_game_attempt table');
      await queryRunner.query(`
        ALTER TABLE "block_game_attempt" 
        DROP COLUMN "total_score"
      `);
    }

    // Remove score column if it exists
    const hasScoreColumn = await queryRunner.hasColumn('block_game_attempt', 'score');
    if (hasScoreColumn) {
      console.log('Removing score column from block_game_attempt table');
      await queryRunner.query(`
        ALTER TABLE "block_game_attempt" 
        DROP COLUMN "score"
      `);
    }

    console.log('FixBlockGameAttemptMissingColumns rollback completed successfully');
  }
}
