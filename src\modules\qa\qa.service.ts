import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import { QAQuestion } from '../../database/entities/qa-question.entity';
import { QAAssignment } from '../../database/entities/qa-assignment.entity';
import { QASubmission } from '../../database/entities/qa-submission.entity';
import { QASubscription } from '../../database/entities/qa-subscription.entity';
import { QAAssignmentStatus } from '../../database/entities/qa-assignment.entity';
import { QASubmissionStatus } from '../../database/entities/qa-submission.entity';
import {
  CreateQAQuestionDto,
  CreateQAAssignmentDto,
  CreateQASubmissionDto,
  QAQuestionResponseDto,
  QAAssignmentResponseDto,
  QASubmissionResponseDto,
  ReviewSubmissionDto,
  UpdateQAQuestionDto,
  QAQuestionPaginationDto,
  QAAssignmentPaginationDto,
  QAUserAssignmentResponseDto,
  QAAssignmentSetResponseDto,
} from '../../database/models/qa.dto';
import { PaginationDto } from 'src/common/models/pagination.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { PaginationService } from '../../common/services/pagination.service';
import { User, UserType } from 'src/database/entities/user.entity';
import { AsyncNotificationHelperService } from '../notification/async-notification-helper.service';
import { NotificationType } from 'src/database/entities/notification.entity';
import { TutorStudentDto } from 'src/database/models/tutor-matching.dto';
import { StudentTutorMapping } from 'src/database/entities/student-tutor-mapping.entity';
import { FeatureType } from 'src/database/entities/plan-feature.entity';
import { QAAssignmentItems } from 'src/database/entities/qa-assignment-items.entity';
import { QAAssignmentSets } from 'src/database/entities/qa-assignment-sets.entity';

@Injectable()
export class QAService {
  private readonly logger = new Logger(QAService.name);

  constructor(
    private readonly paginationService: PaginationService,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(QAQuestion)
    private readonly qaQuestionRepository: Repository<QAQuestion>,
    @InjectRepository(QAAssignment)
    private qaAssignmentRepository: Repository<QAAssignment>,
    @InjectRepository(QAAssignmentItems)
    private qaAssignmentItemsRepository: Repository<QAAssignmentItems>,
    @InjectRepository(QAAssignmentSets)
    private qaAssignmentSetRepository: Repository<QAAssignmentSets>,
    @InjectRepository(QASubmission)
    private qaSubmissionRepository: Repository<QASubmission>,
    @InjectRepository(QASubscription)
    private qaSubscriptionRepository: Repository<QASubscription>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    private readonly asyncNotificationHelper: AsyncNotificationHelperService,
  ) {}

  // async create(questionData: CreateQAQuestionDto, userId?: string): Promise<QAQuestionResponseDto> {
  //   try {
  //     console.log('Creating question with data:', questionData);

  //       const question = this.qaQuestionRepository.create({
  //         question: questionData.question,
  //         points: questionData.points,
  //         minimumWords: questionData.minimumWords,
  //         isActive: true,
  //         createdBy: userId
  //       });
  //       const savedQuestion = await this.qaQuestionRepository.save(question);
  //       console.log('Question entity created:', question);

  //     console.log('Saved question:', savedQuestion);
  //     return {
  //       id: savedQuestion.id,
  //       question: savedQuestion.question,
  //       points: savedQuestion.points,
  //       minimumWords: savedQuestion.minimumWords,
  //       isActive: savedQuestion.isActive,
  //       createdBy: savedQuestion.createdBy,
  //       createdAt: savedQuestion.createdAt,
  //       updatedAt: savedQuestion.updatedAt
  //     };
  //   }catch (error) {
  //     //this.logger.error(`Failed to create question: ${error.message}`, error.stack);
  //     throw new BadRequestException('Failed to create question. Please check your input data.');
  //   }
  // }

  async create(questionData: CreateQAQuestionDto, userId?: string): Promise<QAQuestionResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const existingQuestion = await this.qaQuestionRepository.find({
        where: { isActive: true },
      });

      const quesTitle = existingQuestion.map((q) => q.question.toLowerCase());

      if (quesTitle.includes(questionData.question.toLowerCase())) {
        throw new BadRequestException('Question already exists');
      }

      const question = this.qaQuestionRepository.create({
        question: questionData.question,
        points: questionData.points,
        minimumWords: questionData.minimumWords,
        isActive: true,
        createdBy: userId,
      });

      // Save using the transaction manager only
      const savedQuestion = await queryRunner.manager.save(question);

      await queryRunner.commitTransaction();
      return {
        id: savedQuestion.id,
        question: savedQuestion.question,
        points: savedQuestion.points,
        minimumWords: savedQuestion.minimumWords,
        isActive: savedQuestion.isActive,
        createdBy: savedQuestion.createdBy,
        createdAt: savedQuestion.createdAt,
        updatedAt: savedQuestion.updatedAt,
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
      //throw new BadRequestException('Failed to create question. Please check your input data.');
    } finally {
      await queryRunner.release();
    }
  }

  async getTutorStudents(tutorId: string): Promise<TutorStudentDto[]> {
    try {
      // Check if tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      // Get assignments
      const assignments = await this.studentTutorMappingRepository.find({
        where: {
          tutorId,
          planFeature: {
            type: FeatureType.ENGLISH_QA_WRITING,
          },
        },
        relations: ['student', 'planFeature'],
      });

      // Map to DTOs
      return assignments.map((assignment) => ({
        id: assignment.studentId,
        name: assignment.student.name,
        email: assignment.student.email,
        profilePicture: assignment.student.profilePicture,
        planFeatureId: assignment.planFeatureId,
        moduleName: assignment.planFeature.name,
        moduleType: assignment.planFeature.type,
        status: assignment.status,
        assignedDate: assignment.assignedDate,
        lastActivityDate: assignment.lastActivityDate,
      }));
    } catch (error) {
      this.logger.error(`Error getting tutor students: ${error.message}`, error.stack);
      throw error;
    }
  }

  // async getAllQuestions(paginationDto: PaginationDto,
  //   isActive?: boolean
  // ): Promise<ApiResponse<PagedListDto<QAQuestionResponseDto>>> {
  //   const { skip, take } = this.paginationService.getPaginationParameters(paginationDto);

  //   const query = this.qaQuestionRepository.createQueryBuilder('question');

  //   if (isActive !== undefined) {
  //     query.where('question.isActive = :isActive', { isActive });
  //   }

  //   const [questions, totalCount] = await query
  //     .skip(skip)
  //     .take(take)
  //     .getManyAndCount();

  //   const pagedList = this.paginationService.createPagedList(
  //     questions.map(q => ({
  //       id: q.id,
  //       question: q.question,
  //       points: q.points,
  //       minimumWords: q.minimumWords,
  //       isActive: q.isActive,
  //       createdBy: q.createdBy,
  //       createdAt: q.createdAt,
  //       updatedAt: q.updatedAt
  //     })),
  //     totalCount
  //   );

  //   return ApiResponse.success(
  //     pagedList,
  //     'Questions retrieved successfully'
  //   );
  // }

  // async getAllQuestions(
  //   paginationDto?: QAQuestionPaginationDto
  // ): Promise<ApiResponse<PagedListDto<QAQuestionResponseDto>>>

  async getAllQuestions(paginationDto?: QAQuestionPaginationDto, isActive?: boolean): Promise<ApiResponse<PagedListDto<QAQuestionResponseDto>>> {
    const options: any = {
      where: { isActive: true },
    };

    if (paginationDto) {
      const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;

      if (isActive !== undefined) {
        options.where.isActive = isActive;
      }

      options.skip = (page - 1) * limit;
      options.take = limit;

      if (sortBy && sortDirection) {
        options.order = { [sortBy]: sortDirection };
      } else {
        options.order = { createdAt: 'DESC' };
      }
    }

    const [questions, totalCount] = await this.qaQuestionRepository.findAndCount(options);

    const questionDtos = questions.map((question) => ({
      id: question.id,
      question: question.question,
      points: question.points,
      minimumWords: question.minimumWords,
      isActive: question.isActive,
      createdBy: question.createdBy,
      createdAt: question.createdAt,
      updatedAt: question.updatedAt,
    }));

    const pagedList = new PagedListDto(questionDtos, totalCount);

    return ApiResponse.success(pagedList, 'Questions retrieved successfully');
  }

  async getAllAssignments(paginationDto?: QAAssignmentPaginationDto, isActive?: boolean): Promise<ApiResponse<PagedListDto<QAAssignmentResponseDto>>> {
    const options: any = {
      where: {},
    };

    if (paginationDto) {
      const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;

      options.skip = (page - 1) * limit;
      options.take = limit;

      if (sortBy && sortDirection) {
        options.order = { [sortBy]: sortDirection };
      } else {
        options.order = { createdAt: 'DESC' };
      }
    }

    const [assignments, totalCount] = await this.qaAssignmentRepository.findAndCount(options);

    const assignmentDtos = assignments.map((assignment) => ({
      id: assignment.id,
      studentId: assignment.studentId,
      questionId: assignment.questionId,
      points: assignment.points,
      instructions: assignment.instructions,
      deadline: assignment.deadline,
      assignedDate: assignment.assignedDate,
      status: assignment.status,
      createdBy: assignment.createdBy,
      createdAt: assignment.createdAt,
      updatedAt: assignment.updatedAt,
    }));

    const pagedList = new PagedListDto(assignmentDtos, totalCount);

    return ApiResponse.success(pagedList, 'Assignments retrieved successfully');
  }

  async getLatestAssignment(studentId: string): Promise<QAAssignmentResponseDto> {
    const assignment = await this.qaAssignmentRepository.findOne({
      where: { studentId },
      order: { assignedDate: 'DESC' },
      relations: ['question', 'submission'],
    });

    if (!assignment) {
      throw new NotFoundException('No assignments found for this student');
    }

    return this.transformToAssignmentResponse(assignment);
  }

  //   async getLatestAssignmentItemsv1(studentId: string): Promise<QAAssignmentSetResponseDto> {
  //   // Step 1: Get latest assignment item for this student
  //   const latestItem = await this.qaAssignmentItemsRepository.findOne({
  //     where: { studentId },
  //     order: { assignedDate: 'DESC' },
  //     relations: ['assignmentSet'],
  //   });

  //   if (!latestItem) {
  //     throw new NotFoundException('No assignments found for this student');
  //   }

  //   const latestSetSequence = latestItem.setSequence;

  //   // Step 2: Fetch all assignment items in this latest set
  //   const assignments = await this.qaAssignmentItemsRepository.find({
  //     where: {
  //       studentId,
  //       setSequence: latestSetSequence,
  //     },
  //     relations: ['question', 'assignmentSet', 'submission'],
  //     order: { assignedDate: 'ASC' },
  //   });

  //   if (!assignments.length) {
  //     throw new NotFoundException('No assignments found in the latest set');
  //   }

  //   // Step 3: Construct the response
  //   const assignmentSet = assignments[0].assignmentSet;

  //   return {
  //     setSequence: latestSetSequence,
  //     instructions: assignmentSet?.instructions || '',
  //     studentId,
  //     assignments: assignments.map(a => ({
  //       id: a.id,
  //       studentId: a.studentId, // ✅ add this
  //       questionId: a.questionId,
  //       question: a.question,
  //       points: a.score,
  //       status: a.status,
  //       deadline: a.deadline,
  //       assignedDate: a.assignedDate,
  //       createdAt: a.createdAt,
  //       updatedAt: a.updatedAt,
  //       instructions: assignmentSet?.instructions || '', // ✅ add this
  //       submission: a.submission ? {
  //         id: a.submission.id,
  //         answer: a.submission.answer,
  //         points: a.submission.points,
  //         status: a.submission.status,
  //         submissionDate: a.submission.submissionDate,
  //         feedback: a.submission.feedback,
  //         corrections: a.submission.corrections,
  //         reviewedAt: a.submission.reviewedAt,
  //         reviewedBy: a.submission.reviewedBy,
  //         reviewer: a.submission.reviewer,
  //         setSequence: a.submission.setSequence,
  //         assignmentSet: a.submission.assignmentSet,
  //       } : null,
  //     }))

  //     // assignments: assignments.map(a => ({
  //     //   id: a.id,
  //     //   questionId: a.questionId,
  //     //   question: a.question,
  //     //   points: a.score,
  //     //   status: a.status,
  //     //   deadline: a.deadline,
  //     //   assignedDate: a.assignedDate,
  //     //   createdAt: a.createdAt,
  //     //   updatedAt: a.updatedAt,
  //     //   submission: a.submission ? {
  //     //     id: a.submission.id,
  //     //     answer: a.submission.answer,
  //     //     points: a.submission.points,
  //     //     status: a.submission.status,
  //     //     submissionDate: a.submission.submissionDate,
  //     //     feedback: a.submission.feedback,
  //     //     corrections: a.submission.corrections,
  //     //     reviewedAt: a.submission.reviewedAt,
  //     //     reviewedBy: a.submission.reviewedBy,
  //     //     reviewer: a.submission.reviewer,
  //     //     setSequence: a.submission.setSequence,
  //     //     assignmentSet: a.submission.assignmentSet,
  //     //   } : null,
  //     // })),
  //   };
  // }

  // async getLatestAssignmentItems(studentId: string): Promise<QAAssignmentSetResponseDto> {
  //   // Step 1: Get all assignment items for the student
  //   const allItems = await this.qaAssignmentItemsRepository.find({
  //     where: { studentId },
  //     relations: ['question', 'assignmentSet', 'submission'],
  //     order: {
  //       setSequence: 'DESC',
  //       createdAt: 'DESC',
  //     },
  //   });

  //   if (!allItems.length) {
  //     throw new NotFoundException('No assignments found for this student');
  //   }

  //   // Step 2: Group by setSequence
  //   const groupedBySet = new Map<number, QAAssignmentItems[]>();
  //   for (const item of allItems) {
  //     if (!groupedBySet.has(item.setSequence)) {
  //       groupedBySet.set(item.setSequence, []);
  //     }
  //     groupedBySet.get(item.setSequence).push(item);
  //   }

  //   // Step 3: Pick the first/latest group
  //   const [latestSetSequence, latestItems] = Array.from(groupedBySet.entries())[0];

  //   if (!latestItems || !latestItems.length) {
  //     throw new NotFoundException('No valid assignments found in the latest set');
  //   }

  //   const assignmentSet = latestItems[0].assignmentSet;

  //   // Step 4: Build the response
  //   return {
  //     setSequence: latestSetSequence,
  //     instructions: assignmentSet?.instructions || '',
  //     studentId,
  //     assignments: latestItems.map(a => ({
  //       id: a.id,
  //       studentId: a.studentId,
  //       questionId: a.questionId,
  //       question: a.question,
  //       points: a.score,
  //       status: a.status,
  //       deadline: a.deadline,
  //       assignedDate: a.assignedDate,
  //       createdAt: a.createdAt,
  //       updatedAt: a.updatedAt,
  //       submission: a.submission ? {
  //         id: a.submission.id,
  //         answer: a.submission.answer,
  //         points: a.submission.points,
  //         status: a.submission.status,
  //         submissionDate: a.submission.submissionDate,
  //         feedback: a.submission.feedback,
  //         corrections: a.submission.corrections,
  //         reviewedAt: a.submission.reviewedAt,
  //         reviewedBy: a.submission.reviewedBy,
  //         reviewer: a.submission.reviewer,
  //         setSequence: a.submission.setSequence,
  //         assignmentSet: a.submission.assignmentSet,
  //       } : null,
  //     })),
  //   };
  // }

  // async getLatestAssignmentItemsv2(studentId: string): Promise<QAAssignmentSetResponseDto> {
  //   // Step 1: Fetch all assignment items for the student, ordered
  //   const allItems = await this.qaAssignmentItemsRepository.find({
  //     where: { studentId },
  //     relations: ['question', 'assignmentSet', 'submission'],
  //     order: {
  //       setSequence: 'DESC',
  //       createdAt: 'DESC',
  //     },
  //   });

  //   if (!allItems.length) {
  //     throw new NotFoundException('No assignments found for this student');
  //   }

  //   // Step 2: Group by setSequence
  //   const groupedBySet = new Map<number, QAAssignmentItems[]>();
  //   for (const item of allItems) {
  //     if (!groupedBySet.has(item.setSequence)) {
  //       groupedBySet.set(item.setSequence, []);
  //     }
  //     groupedBySet.get(item.setSequence).push(item);
  //   }

  //   // Step 3: Get the first (latest) group
  //   const [latestSetSequence, latestItems] = Array.from(groupedBySet.entries())[0];

  //   if (!latestItems || !latestItems.length) {
  //     throw new NotFoundException('No valid assignments found in the latest set');
  //   }

  //   const assignmentSet = latestItems[0].assignmentSet;
  //   const instructions = assignmentSet?.instructions || '';

  //   // Step 4: Construct response
  //   return {
  //     setSequence: latestSetSequence,
  //     instructions,
  //     studentId,
  //     assignments: latestItems.map(a => ({
  //       id: a.id,
  //       studentId: a.studentId,
  //       questionId: a.questionId,
  //       question: a.question,
  //       points: a.score,
  //       status: a.status,
  //       deadline: a.deadline,
  //       assignedDate: a.assignedDate,
  //       createdAt: a.createdAt,
  //       updatedAt: a.updatedAt,
  //       instructions,
  //       submission: a.submission ? {
  //         id: a.submission.id,
  //         answer: a.submission.answer,
  //         points: a.submission.points,
  //         status: a.submission.status,
  //         submissionDate: a.submission.submissionDate,
  //         feedback: a.submission.feedback,
  //         corrections: a.submission.corrections,
  //         reviewedAt: a.submission.reviewedAt,
  //         reviewedBy: a.submission.reviewedBy,
  //         reviewer: a.submission.reviewer,
  //         setSequence: a.submission.setSequence,
  //         assignmentSet: a.submission.assignmentSet,
  //       } : null,
  //     })),
  //   };
  // }

  async getLatestAssignmentItems(studentId: string): Promise<QAAssignmentSetResponseDto> {
    const allItems = await this.qaAssignmentItemsRepository.find({
      where: { studentId },
      relations: ['question', 'assignmentSet', 'submission'],
      order: {
        setSequence: 'DESC',
        createdAt: 'DESC',
      },
    });

    if (!allItems.length) {
      // Return empty response instead of throwing error
      return {
        setSequence: 0,
        assignmentScore: 0,
        instructions: '',
        studentId: studentId,
        assignments: [],
        submissions: [],
      };
    }

    const groupedBySet = new Map<number, QAAssignmentItems[]>();
    for (const item of allItems) {
      if (!groupedBySet.has(item.setSequence)) {
        groupedBySet.set(item.setSequence, []);
      }
      groupedBySet.get(item.setSequence).push(item);
    }

    // Step 3: Get the first (latest) group
    const [latestSetSequence, latestItems] = Array.from(groupedBySet.entries())[0];

    if (!latestItems || !latestItems.length) {
      throw new NotFoundException('No valid assignments found in the latest set');
    }

    const assignmentSet = await this.qaAssignmentSetRepository.findOne({
      where: { setSequence: allItems[0].setSequence },
    });

    if (!assignmentSet) {
      throw new NotFoundException('Assignment set not found.');
    }

    const submissions = await this.qaSubmissionRepository.find({
      where: { setSequence: allItems[0].setSequence },
    });

    return {
      setSequence: assignmentSet.setSequence,
      instructions: assignmentSet.instructions,
      assignmentScore: assignmentSet.score,
      studentId: studentId,
      assignments: latestItems.map((a) => ({
        id: a.id,
        questionId: a.questionId,
        studentId: a.studentId,
        points: a.score,
        status: a.status,
        deadline: a.deadline,
        instructions: assignmentSet.instructions,
        assignedDate: a.assignedDate,
        createdAt: a.createdAt,
        updatedAt: a.updatedAt,
        question: a.question,
      })),
      submissions: submissions.map((s) => ({
        id: s.id,
        answer: s.answer,
        score: s.points,
        status: s.status,
        submissionDate: s.submissionDate,
        feedback: s.feedback,
        corrections: s.corrections,
        reviewedAt: s.reviewedAt,
        reviewedBy: s.reviewedBy,
        setSequence: s.setSequence,
        reviewer: s.reviewer,
      })),
    };
  }

  async getAssignment(id: string): Promise<QAAssignmentResponseDto> {
    const assignment = await this.qaAssignmentRepository.findOne({
      where: { id },
      relations: ['question', 'submission'],
    });

    if (!assignment) {
      throw new NotFoundException('Assignment not found');
    }

    return this.transformToAssignmentResponse(assignment);
  }

  async getStudentAssignments(studentId: string): Promise<QAAssignmentResponseDto[]> {
    const assignments = await this.qaAssignmentRepository.find({
      where: { studentId },
      relations: ['question', 'submission'],
      order: { assignedDate: 'DESC' },
    });

    return assignments.map((assignment) => this.transformToAssignmentResponse(assignment));
  }

  async createSubmission(dto: CreateQASubmissionDto, studentId: string): Promise<QASubmissionResponseDto> {
    const assignment = await this.qaAssignmentItemsRepository.findOne({
      where: { setSequence: dto.setSequence, studentId },
    });

    if (!assignment) {
      throw new NotFoundException('Assignment not found');
    }

    if (assignment.status === QAAssignmentStatus.EXPIRED) {
      throw new BadRequestException('Cannot submit to an expired assignment');
    }

    let submission = await this.qaSubmissionRepository.findOne({
      where: { setSequence: dto.setSequence },
    });

    if (!submission) {
      submission = this.qaSubmissionRepository.create({
        ...dto,
        status: QASubmissionStatus.DRAFT,
      });
    } else {
      submission.answer = dto.answer;
    }

    assignment.status = QAAssignmentStatus.IN_PROGRESS;
    await this.qaAssignmentRepository.save(assignment);

    const savedSubmission = await this.qaSubmissionRepository.save(submission);
    return this.transformToSubmissionResponse(savedSubmission);
  }

  async submitFinalSubmission(submissionId: string, studentId: string): Promise<QASubmissionResponseDto> {
    const submission = await this.qaSubmissionRepository.findOne({
      where: { id: submissionId },
      relations: ['assignmentSet'],
    });

    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    // if (submission.assignment.studentId !== studentId) {
    //   throw new BadRequestException('Unauthorized to submit this assignment');
    // }

    if (submission.status === QASubmissionStatus.SUBMITTED) {
      throw new BadRequestException('Submission already finalized');
    }

    submission.status = QASubmissionStatus.SUBMITTED;
    submission.submissionDate = new Date();
    //submission.assignment.status = QAAssignmentStatus.COMPLETED;

    //await this.qaAssignmentRepository.save(submission.assignment);
    const savedSubmission = await this.qaSubmissionRepository.save(submission);

    // Send notification to tutor about student submission
    try {
      // Find the tutor assigned to this student
      const studentTutorMapping = await this.studentTutorMappingRepository.findOne({
        where: { studentId },
        relations: ['tutor'],
      });

      if (studentTutorMapping && studentTutorMapping.tutor) {
        await this.asyncNotificationHelper.notifyAsync(
          studentTutorMapping.tutor.id,
          NotificationType.QA_SUBMISSION,
          'New Q&A Submission',
          `A student has submitted their Q&A answer and is ready for review.`,
          {
            relatedEntityId: savedSubmission.id,
            relatedEntityType: 'qa_submission',
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: true,
          },
          {
            submissionId: savedSubmission.id,
            entryType: 'qa_submission',
            priority: 2, // Medium priority
          }
        );
      }
    } catch (error) {
      this.logger.error(`Failed to send Q&A submission notification to tutor for student ${studentId}: ${error.message}`);
    }

    return this.transformToSubmissionResponse(savedSubmission);
  }

  // private transformToAssignmentResponse(assignment: QAAssignment): QAAssignmentResponseDto {
  //   if (!assignment.question) {
  //     throw new NotFoundException('Question not found for assignment');
  //   }

  //   return {
  //     id: assignment.id,
  //     question: {
  //       id: assignment.question.id,
  //       question: assignment.question.question,
  //       points: assignment.question.points,
  //       minimumWords: assignment.question.minimumWords,
  //       isActive: assignment.question.isActive,
  //       createdBy: assignment.question.createdBy,
  //       createdAt: assignment.question.createdAt,
  //       updatedAt: assignment.question.updatedAt
  //     },
  //     points: assignment.points,
  //     deadline: assignment.deadline,
  //     instructions: assignment.instructions,
  //     status: assignment.status,
  //     assignedDate: assignment.assignedDate,
  //     createdAt: assignment.createdAt,
  //     updatedAt: assignment.updatedAt
  //   };
  // }

  private transformToAssignmentResponse(assignment: QAAssignment): QAUserAssignmentResponseDto {
    if (!assignment) {
      throw new NotFoundException('Assignment not found.');
    }

    const submission = assignment.submission
      ? {
          id: assignment.submission.id,
          answer: assignment.submission.answer,
          status: assignment.submission.status,
          submissionDate: assignment.submission.submissionDate,
          feedback: assignment.submission.feedback,
          corrections: assignment.submission.corrections,
          reviewedAt: assignment.submission.reviewedAt,
          reviewedBy: assignment.submission.reviewedBy,
          // points: assignment.submission.points ? assignment.submission.points : 0
        }
      : null;

    const question = assignment.question
      ? {
          id: assignment.question.id,
          question: assignment.question.question,
          points: assignment.question.points,
          isActive: assignment.question.isActive,
          minimumWords: assignment.question.minimumWords,
          createdBy: assignment.question.createdBy,
          createdAt: assignment.question.createdAt,
          updatedAt: assignment.question.updatedAt,
        }
      : null;

    return {
      id: assignment.id,
      questionId: assignment.questionId,
      question,
      studentId: assignment.studentId,
      points: assignment.points,
      deadline: assignment.deadline,
      instructions: assignment.instructions,
      status: assignment.status,
      assignedDate: assignment.assignedDate,
      createdAt: assignment.createdAt,
      updatedAt: assignment.updatedAt,
      submission,
    };
  }

  // private transformToAssignmentResponse(assignment: QAAssignment): QAAssignmentResponseDto {
  //   if (!assignment) {
  //     throw new NotFoundException('Assignment not found.');
  //   }

  //   return {
  //     id: assignment.id,
  //     questionId: assignment.questionId,
  //     studentId: assignment.studentId,
  //     createdAt: assignment.createdAt,
  //     updatedAt: assignment.updatedAt,
  //     points: assignment.points,
  //     deadline: assignment.deadline,
  //     instructions: assignment.instructions,
  //     status: assignment.status,
  //     assignedDate: assignment.assignedDate,
  //   };
  // }

  private transformToSubmissionResponse(submission: QASubmission): QASubmissionResponseDto {
    return {
      id: submission.id,
      score: submission.points,
      answer: submission.answer,
      status: submission.status,
      submissionDate: submission.submissionDate,
      feedback: submission.feedback,
      corrections: submission.corrections,
      title: submission.assignmentSet?.instructions || submission.assignment?.instructions || submission.assignment?.question?.question || null,
      createdAt: submission.createdAt,
      updatedAt: submission.updatedAt,
    };
  }

  async updateQuestion(id: string, dto: UpdateQAQuestionDto): Promise<QAQuestionResponseDto> {
    const question = await this.qaQuestionRepository.findOne({ where: { id } });
    if (!question) {
      throw new NotFoundException('Question not found');
    }

    Object.assign(question, dto);
    const savedQuestion = await this.qaQuestionRepository.save(question);
    return {
      id: savedQuestion.id,
      question: savedQuestion.question,
      points: savedQuestion.points,
      minimumWords: savedQuestion.minimumWords,
      isActive: savedQuestion.isActive,
      createdBy: savedQuestion.createdBy,
      createdAt: savedQuestion.createdAt,
      updatedAt: savedQuestion.updatedAt,
    };
  }

  async deleteQuestion(id: string): Promise<void> {
    const question = await this.qaQuestionRepository.findOne({ where: { id } });
    if (!question) {
      throw new NotFoundException('Question not found');
    }

    question.isActive = false;
    await this.qaQuestionRepository.save(question);
  }

  async createAssignments(dto: CreateQAAssignmentDto): Promise<QAAssignmentResponseDto[]> {
    const question = await this.qaQuestionRepository.findOne({
      where: { id: dto.questionId, isActive: true },
    });

    if (!question) {
      throw new NotFoundException('Question not found or inactive');
    }

    const assignment = this.qaAssignmentRepository.create({
      questionId: dto.questionId,
      studentId: dto.studentId,
      deadline: dto.deadline,
      instructions: dto.instructions,
      points: dto.points || question.points,
      assignedDate: new Date(Date.now()),
      status: QAAssignmentStatus.ASSIGNED,
    });

    const savedAssignment = await this.qaAssignmentRepository.save(assignment);
    return [this.transformToAssignmentResponse(savedAssignment)];
  }

  async getPendingSubmissions(tutorId: string, paginationDto: PaginationDto): Promise<ApiResponse<PagedListDto<QASubmissionResponseDto>>> {
    const { skip, take } = this.paginationService.getPaginationParameters(paginationDto);

    const assignedStudents = await this.getTutorStudents(tutorId);
    const studentUserIds = assignedStudents.map((s) => s.id);

    if (studentUserIds.length === 0) {
      return ApiResponse.success(this.paginationService.createPagedList([], 0, paginationDto), 'No pending submissions found for your students');
    }

    const [submissions, totalCount] = await this.qaSubmissionRepository.findAndCount({
      where: {
        status: QASubmissionStatus.SUBMITTED,
        createdBy: In(studentUserIds),
      },
      skip,
      take,
      relations: ['assignmentSet', 'assignment', 'assignment.question'],
    });

    const pagedList = this.paginationService.createPagedList(
      submissions.map((s) => this.transformToSubmissionResponse(s)),
      totalCount,
      paginationDto,
    );

    return ApiResponse.success(pagedList, 'Pending submissions retrieved successfully');
  }

  async getReviewedSubmissions(tutorId: string, paginationDto: PaginationDto): Promise<ApiResponse<PagedListDto<QASubmissionResponseDto>>> {
    const { skip, take } = this.paginationService.getPaginationParameters(paginationDto);

    const [submissions, totalCount] = await this.qaSubmissionRepository.findAndCount({
      where: {
        reviewedBy: tutorId,
        status: QASubmissionStatus.REVIEWED,
      },
      skip,
      take,
      relations: ['assignment', 'assignment.question'],
    });

    const pagedList = this.paginationService.createPagedList(
      submissions.map((s) => this.transformToSubmissionResponse(s)),
      totalCount,
      paginationDto,
    );

    return ApiResponse.success(pagedList, 'Reviewed submissions retrieved successfully');
  }

  async reviewSubmission(id: string, dto: ReviewSubmissionDto, tutorId: string): Promise<QASubmissionResponseDto> {
    const submission = await this.qaSubmissionRepository.findOne({
      where: {
        id: id,
        //status: QASubmissionStatus.SUBMITTED || QASubmissionStatus.REVIEWED
      },
      relations: ['assignment'],
    });

    if (!submission) {
      throw new NotFoundException('Submission not found or already reviewed');
    }

    submission.feedback = dto.feedback ? dto.feedback : submission.feedback;
    submission.points = dto.score ? dto.score : submission.points;
    submission.corrections = dto.corrections ? dto.corrections : submission.corrections;
    submission.status = QASubmissionStatus.REVIEWED;
    submission.reviewedBy = tutorId;
    submission.reviewedAt = new Date();

    const savedSubmission = await this.qaSubmissionRepository.save(submission);

    // Send notification to student about evaluation
    try {
      // Find the student who submitted this Q&A by getting the assignment item
      const assignmentItem = await this.qaAssignmentItemsRepository.findOne({
        where: { setSequence: savedSubmission.setSequence },
      });

      if (assignmentItem && assignmentItem.studentId) {
        await this.asyncNotificationHelper.notifyAsync(
          assignmentItem.studentId,
          NotificationType.QA_FEEDBACK,
          'Q&A Evaluation Complete',
          `Your Q&A submission has been evaluated by your tutor. Check your feedback and score!`,
          {
            relatedEntityId: savedSubmission.id,
            relatedEntityType: 'qa_submission',
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: true,
          },
          {
            submissionId: savedSubmission.id,
            entryType: 'qa_evaluation',
            priority: 2, // Medium priority
          }
        );
      }
    } catch (error) {
      this.logger.error(`Failed to send Q&A evaluation notification to student for submission ${savedSubmission.id}: ${error.message}`);
    }

    return this.transformToSubmissionResponse(savedSubmission);
  }

  // async getAllAssignments(
  //   paginationDto: PaginationDto,
  //   status?: string
  // ): Promise<PagedListDto<QAAssignmentResponseDto>> {
  //   const { skip, take } = this.paginationService.getPaginationParameters(paginationDto);

  //   const query = this.qaAssignmentRepository.createQueryBuilder('assignment')
  //     .leftJoinAndSelect('assignment.question', 'question')
  //     .leftJoinAndSelect('assignment.submission', 'submission');

  //   if (status) {
  //     query.where('assignment.status = :status', { status });
  //   }

  //   const [assignments, totalCount] = await query
  //     .skip(skip)
  //     .take(take)
  //     .getManyAndCount();

  //   return this.paginationService.createPagedList(
  //     assignments.map(assignment => this.transformToAssignmentResponse(assignment)),
  //     totalCount,
  //     paginationDto
  //   );
  // }

  /**
   * Find a QA question by ID
   * @param id Question ID
   * @returns QA question
   */
  async findById(id: string): Promise<QAQuestionResponseDto> {
    try {
      const question = await this.qaQuestionRepository.findOne({
        where: { id, isActive: true },
        relations: ['assignments'],
      });

      if (!question) {
        throw new NotFoundException(`QA question with ID ${id} not found`);
      }

      return this.transformToQuestionResponse(question);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch QA question: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to fetch QA question');
    }
  }

  private transformToQuestionResponse(question: QAQuestion): QAQuestionResponseDto {
    return {
      id: question.id,
      question: question.question,
      minimumWords: question.minimumWords,
      points: question.points,
      isActive: question.isActive,
      createdBy: question.createdBy,
      createdAt: question.createdAt,
      updatedAt: question.updatedAt,
      // Include any other fields from your QAQuestionResponseDto
    };
  }

  /**
   * Get a list of students for dropdown with search functionality
   * @param searchQuery Optional search query for filtering students
   * @returns List of students with basic information
   */
  async getStudentDropdownList(searchQuery?: string): Promise<any[]> {
    try {
      // Create query builder
      const queryBuilder = this.userRepository
        .createQueryBuilder('user')
        .select(['user.id as id', 'user.name as name', 'user.userId as userId', 'user.email as email'])
        .where('user.isActive = :isActive', { isActive: true })
        .andWhere('user.type = :type', { type: UserType.STUDENT });

      // Add search filter if provided
      if (searchQuery && searchQuery.trim().length > 0) {
        // Use partial matching if search query has at least 3 characters
        if (searchQuery.length >= 3) {
          queryBuilder.andWhere('(LOWER(user.name) LIKE LOWER(:search) OR ' + 'LOWER(user.userId) LIKE LOWER(:search) OR ' + 'LOWER(user.email) LIKE LOWER(:search))', { search: `%${searchQuery}%` });
        } else {
          // Use exact matching for short queries
          queryBuilder.andWhere('(LOWER(user.name) = LOWER(:search) OR ' + 'LOWER(user.userId) = LOWER(:search) OR ' + 'LOWER(user.email) = LOWER(:search))', { search: searchQuery });
        }
      }

      // Order by name for better usability
      queryBuilder.orderBy('user.name', 'ASC');

      // Limit results for performance
      queryBuilder.limit(50);

      // Execute raw query to get the exact fields we need
      const students = await queryBuilder.getRawMany();

      return students;
    } catch (error) {
      this.logger.error(`Failed to fetch student dropdown list: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to fetch student dropdown list');
    }
  }
}
