-- <PERSON><PERSON>t to diagnose and fix diary skin registry timeout issues
-- Run this to check current database state and performance

-- 1. Check current indexes on diary_skin_registry table
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'diary_skin_registry'
ORDER BY indexname;

-- 2. Check table statistics
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_rows,
    n_dead_tup as dead_rows,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables 
WHERE relname = 'diary_skin_registry';

-- 3. Check for any long-running transactions that might be causing locks
SELECT 
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query,
    state,
    wait_event_type,
    wait_event
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
    AND query ILIKE '%diary_skin_registry%';

-- 4. Check for locks on the diary_skin_registry table
SELECT 
    l.locktype,
    l.database,
    l.relation::regclass,
    l.page,
    l.tuple,
    l.virtualxid,
    l.transactionid,
    l.classid,
    l.objid,
    l.objsubid,
    l.virtualtransaction,
    l.pid,
    l.mode,
    l.granted,
    a.usename,
    a.query,
    a.query_start,
    age(now(), a.query_start) AS "age"
FROM pg_locks l
LEFT JOIN pg_stat_activity a ON l.pid = a.pid
WHERE l.relation = 'diary_skin_registry'::regclass
ORDER BY a.query_start;

-- 5. Check for duplicate or orphaned records
SELECT 
    diary_skin_id,
    COUNT(*) as record_count,
    array_agg(id) as registry_ids
FROM diary_skin_registry 
GROUP BY diary_skin_id 
HAVING COUNT(*) > 1;

-- 6. Check recent UPDATE operations that might be slow
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    max_time,
    stddev_time
FROM pg_stat_statements 
WHERE query ILIKE '%UPDATE%diary_skin_registry%'
ORDER BY mean_time DESC
LIMIT 10;

-- 7. Analyze table to update statistics (run this if needed)
-- ANALYZE diary_skin_registry;

-- 8. Check if VACUUM is needed
SELECT 
    relname,
    n_dead_tup,
    n_live_tup,
    round(n_dead_tup::numeric / (n_live_tup + n_dead_tup) * 100, 2) as dead_tuple_percent
FROM pg_stat_user_tables 
WHERE relname = 'diary_skin_registry'
    AND (n_live_tup + n_dead_tup) > 0;

-- 9. Check current database configuration for timeouts
SHOW statement_timeout;
SHOW lock_timeout;
SHOW idle_in_transaction_session_timeout;
