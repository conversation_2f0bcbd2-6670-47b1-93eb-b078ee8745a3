# Diary Skin Registry Timeout Fix

## Issue Description

The application was experiencing timeout errors when updating diary skins, specifically during UPDATE operations on the `diary_skin_registry` table:

```
query failed: UPDATE "diary_skin_registry" SET "updated_at" = $1, "file_path" = $2, "file_size" = $3, "user_id" = $4 WHERE "id" IN ($5) RETURNING "updated_at"
error: error: canceling statement due to statement timeout
```

## Root Cause Analysis

1. **Transaction Timeout**: The UPDATE operation was taking longer than the configured 30-second statement timeout
2. **Missing Indexes**: The `diary_skin_registry` table lacked proper indexes for efficient lookups
3. **Inefficient Operation**: Using `save()` instead of `insert()` was causing TypeORM to perform UPDATE operations instead of INSERT
4. **Lock Contention**: Long-running transactions could cause lock contention on the table

## Solutions Implemented

### 1. Code Changes

#### A. Improved Registry Entry Creation
- **File**: `src/modules/diary/diary-skin.service.ts`
- **Change**: Replaced `queryRunner.manager.save()` with `queryRunner.manager.insert()` for new registry entries
- **Benefit**: Avoids UPDATE operations that could timeout, ensures INSERT operations are used for new records

```typescript
// Before
await queryRunner.manager.save(registryEntry);

// After
try {
  await queryRunner.manager.insert(DiarySkinRegistry, registryEntry);
} catch (insertError) {
  // Fallback to save if insert fails
  await queryRunner.manager.save(registryEntry);
}
```

#### B. Enhanced Transaction Management
- **File**: `src/modules/diary/diary-skin.service.ts`
- **Change**: Replaced manual transaction handling with `TransactionHelper`
- **Benefit**: Better timeout configuration, retry logic, and error handling

```typescript
// Before
const queryRunner = this.dataSource.createQueryRunner();
await queryRunner.connect();
await queryRunner.startTransaction();

// After
return await TransactionHelper.executeInTransaction(
  this.dataSource,
  async (queryRunner) => { /* operation */ },
  {
    isolationLevel: 'READ COMMITTED',
    timeout: 60000, // 60 seconds for file operations
    retryOnDeadlock: true,
  }
);
```

### 2. Database Optimizations

#### A. Performance Indexes Migration
- **File**: `src/database/migrations/1754544400000-add-diary-skin-registry-performance-indexes.ts`
- **Indexes Added**:
  - `IDX_diary_skin_registry_diary_skin_id`: For efficient DELETE operations by diary_skin_id
  - `IDX_diary_skin_registry_user_id`: For user-specific queries
  - `IDX_diary_skin_registry_skin_updated`: Composite index for cleanup operations

#### B. Diagnostic Script
- **File**: `scripts/fix-diary-skin-timeout.sql`
- **Purpose**: Diagnose database performance issues, check locks, and analyze table statistics

## Configuration Changes

### Transaction Timeouts
- **File Upload Operations**: Increased timeout to 60 seconds
- **Isolation Level**: Set to `READ COMMITTED` for optimal performance
- **Retry Logic**: Enabled retry on deadlock for better reliability

## Testing and Validation

### 1. Run the Migration
```bash
npm run migration:run
```

### 2. Check Database Performance
```bash
psql -d your_database -f scripts/fix-diary-skin-timeout.sql
```

### 3. Monitor Application Logs
- Look for successful registry entry creation logs
- Monitor transaction completion times
- Check for any remaining timeout errors

## Prevention Measures

1. **Regular Database Maintenance**:
   - Run `ANALYZE` on registry tables periodically
   - Monitor dead tuple percentages
   - Vacuum tables when needed

2. **Code Best Practices**:
   - Use `insert()` for new records when possible
   - Implement proper transaction timeouts
   - Use TransactionHelper for complex operations

3. **Monitoring**:
   - Set up alerts for slow queries (>5 seconds)
   - Monitor lock wait events
   - Track transaction rollback rates

## Related Files Modified

- `src/modules/diary/diary-skin.service.ts`: Main service fix
- `src/database/migrations/1754544400000-add-diary-skin-registry-performance-indexes.ts`: Database indexes
- `scripts/fix-diary-skin-timeout.sql`: Diagnostic script
- `docs/fixes/DIARY_SKIN_TIMEOUT_FIX.md`: This documentation

## Expected Outcomes

1. **Eliminated Timeouts**: UPDATE operations should complete within acceptable time limits
2. **Improved Performance**: Faster registry operations due to proper indexing
3. **Better Error Handling**: More descriptive error messages and retry logic
4. **Enhanced Monitoring**: Better visibility into database performance issues

## Rollback Plan

If issues persist:

1. Revert code changes in `diary-skin.service.ts`
2. Drop the new indexes if they cause performance issues
3. Restore original transaction handling
4. Contact database administrator for further analysis
